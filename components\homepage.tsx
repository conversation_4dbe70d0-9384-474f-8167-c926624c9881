"use client"

import React, { useState, useEffect } from "react"
import Link from "next/link"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs"
import Navigation from "./navigation"
import {
  ArrowRight,
  Zap,
  Palette,
  Download,
  Upload,
  Settings,
  Code,
  Image as ImageIcon,
  Sparkles,
  Star,
  Users,
  Globe,
  Cpu,
  Monitor,
  Smartphone,
  Tablet
} from "lucide-react"

export default function Homepage() {
  const [currentExample, setCurrentExample] = useState(0)
  const [isVisible, setIsVisible] = useState(false)
  const [animatedText, setAnimatedText] = useState("")
  const [textIndex, setTextIndex] = useState(0)

  const animationTexts = [
    "Transform Images into ASCII Art",
    "Create Stunning Text-Based Graphics",
    "Build Terminal-Ready Artwork",
    "Generate Code Documentation Headers"
  ]

  useEffect(() => {
    setIsVisible(true)

    // Animated text effect
    const interval = setInterval(() => {
      setTextIndex((prev) => (prev + 1) % animationTexts.length)
    }, 3000)

    return () => clearInterval(interval)
  }, [])

  useEffect(() => {
    const text = animationTexts[textIndex]
    let currentIndex = 0
    setAnimatedText("")

    const typeInterval = setInterval(() => {
      if (currentIndex <= text.length) {
        setAnimatedText(text.slice(0, currentIndex))
        currentIndex++
      } else {
        clearInterval(typeInterval)
      }
    }, 50)

    return () => clearInterval(typeInterval)
  }, [textIndex])

  const examples = [
    {
      title: "Portrait Art",
      description: "Transform photos into stunning ASCII portraits",
      ascii: `    @@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@
    @@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@
    @@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@
    @@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@
    @@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@
    @@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@
    @@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@
    @@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@`,
      category: "Portrait"
    },
    {
      title: "Landscape Scene",
      description: "Convert scenic photos into beautiful ASCII landscapes",
      ascii: `    ░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░
    ░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░
    ▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒
    ▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓
    ████████████████████████████████████████████████████████████████████████████████████████████████████
    ████████████████████████████████████████████████████████████████████████████████████████████████████
    ████████████████████████████████████████████████████████████████████████████████████████████████████
    ████████████████████████████████████████████████████████████████████████████████████████████████████`,
      category: "Landscape"
    },
    {
      title: "Abstract Art",
      description: "Create unique ASCII patterns from abstract images",
      ascii: `    .:*#%@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@
    .:*#%@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@
    .:*#%@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@
    .:*#%@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@
    .:*#%@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@
    .:*#%@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@
    .:*#%@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@
    .:*#%@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@`,
      category: "Abstract"
    }
  ]

  const features = [
    {
      icon: <Zap className="h-8 w-8" />,
      title: "Lightning Fast",
      description: "Real-time ASCII conversion with optimized algorithms for instant results"
    },
    {
      icon: <Palette className="h-8 w-8" />,
      title: "Color Support",
      description: "Choose between grayscale and full-color ASCII art with customizable palettes"
    },
    {
      icon: <Settings className="h-8 w-8" />,
      title: "Advanced Controls",
      description: "Fine-tune resolution, character sets, and inversion for perfect results"
    },
    {
      icon: <Download className="h-8 w-8" />,
      title: "Export Options",
      description: "Download your ASCII art as text files or copy to clipboard instantly"
    },
    {
      icon: <Upload className="h-8 w-8" />,
      title: "Easy Upload",
      description: "Drag and drop images or browse files with support for all major formats"
    },
    {
      icon: <Code className="h-8 w-8" />,
      title: "Developer Friendly",
      description: "Clean, readable output perfect for code comments and documentation"
    }
  ]

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900">
      <Navigation />
      {/* Hero Section */}
      <section className="relative overflow-hidden">
        <div className="absolute inset-0 bg-gradient-to-r from-purple-800/20 to-pink-800/20" />

        {/* Floating ASCII Characters Background */}
        <div className="absolute inset-0 overflow-hidden pointer-events-none">
          <div className="absolute top-20 left-10 text-purple-300/20 text-6xl font-mono animate-bounce" style={{animationDelay: '0s', animationDuration: '3s'}}>@</div>
          <div className="absolute top-40 right-20 text-pink-300/20 text-4xl font-mono animate-bounce" style={{animationDelay: '1s', animationDuration: '4s'}}>#</div>
          <div className="absolute top-60 left-1/4 text-purple-300/20 text-5xl font-mono animate-bounce" style={{animationDelay: '2s', animationDuration: '3.5s'}}>%</div>
          <div className="absolute bottom-40 right-1/3 text-pink-300/20 text-3xl font-mono animate-bounce" style={{animationDelay: '0.5s', animationDuration: '4.5s'}}>*</div>
          <div className="absolute bottom-60 left-20 text-purple-300/20 text-4xl font-mono animate-bounce" style={{animationDelay: '1.5s', animationDuration: '3s'}}>+</div>
          <div className="absolute top-32 right-1/4 text-pink-300/20 text-5xl font-mono animate-bounce" style={{animationDelay: '2.5s', animationDuration: '4s'}}>█</div>
        </div>

        <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 pt-20 pb-16">
          <div className={`text-center transition-all duration-1000 ${isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-10'}`}>
            <div className="flex justify-center mb-6">
              <Badge variant="secondary" className="bg-purple-100 text-purple-800 px-4 py-2 text-sm font-medium">
                <Sparkles className="h-4 w-4 mr-2" />
                Advanced ASCII Art Generator
              </Badge>
            </div>
            
            <h1 className="text-5xl md:text-7xl font-bold text-white mb-6 leading-tight">
              <span className="bg-gradient-to-r from-purple-400 to-pink-400 bg-clip-text text-transparent block min-h-[1.2em]">
                {animatedText}
                <span className="animate-pulse">|</span>
              </span>
            </h1>
            
            <p className="text-xl text-gray-300 mb-8 max-w-3xl mx-auto leading-relaxed">
              Experience the most advanced ASCII art converter on the web. Transform any image into beautiful 
              text-based artwork with real-time preview, customizable settings, and professional-grade output.
            </p>
            
            <div className="flex flex-col sm:flex-row gap-4 justify-center items-center">
              <Link href="/converter">
                <Button size="lg" className="bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700 text-white px-8 py-4 text-lg font-semibold rounded-full shadow-lg hover:shadow-xl transition-all duration-300">
                  Start Creating
                  <ArrowRight className="ml-2 h-5 w-5" />
                </Button>
              </Link>
              <Button variant="outline" size="lg" className="border-white/20 text-white hover:bg-white/10 px-8 py-4 text-lg rounded-full">
                View Examples
              </Button>
            </div>
          </div>
        </div>
      </section>

      {/* Statistics Section */}
      <section className="py-16 bg-slate-800/30">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-2 md:grid-cols-4 gap-8 text-center">
            <div className="space-y-2">
              <div className="text-4xl md:text-5xl font-bold text-white">100K+</div>
              <div className="text-gray-300 text-sm md:text-base">Images Converted</div>
            </div>
            <div className="space-y-2">
              <div className="text-4xl md:text-5xl font-bold text-white">50K+</div>
              <div className="text-gray-300 text-sm md:text-base">Active Users</div>
            </div>
            <div className="space-y-2">
              <div className="text-4xl md:text-5xl font-bold text-white">15+</div>
              <div className="text-gray-300 text-sm md:text-base">File Formats</div>
            </div>
            <div className="space-y-2">
              <div className="text-4xl md:text-5xl font-bold text-white">99.9%</div>
              <div className="text-gray-300 text-sm md:text-base">Uptime</div>
            </div>
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section className="py-20 bg-slate-800/50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-4xl font-bold text-white mb-4">Powerful Features</h2>
            <p className="text-xl text-gray-300 max-w-2xl mx-auto">
              Everything you need to create professional ASCII art with precision and style
            </p>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {features.map((feature, index) => (
              <Card key={index} className="bg-slate-700/50 border-slate-600 hover:bg-slate-700/70 transition-all duration-300 hover:scale-105">
                <CardHeader>
                  <div className="text-purple-400 mb-4">
                    {feature.icon}
                  </div>
                  <CardTitle className="text-white text-xl">{feature.title}</CardTitle>
                </CardHeader>
                <CardContent>
                  <CardDescription className="text-gray-300 text-base leading-relaxed">
                    {feature.description}
                  </CardDescription>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* Examples Gallery */}
      <section className="py-20">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-4xl font-bold text-white mb-4">See It In Action</h2>
            <p className="text-xl text-gray-300 max-w-2xl mx-auto">
              Explore different styles and see how our converter handles various types of images
            </p>
          </div>

          <Tabs defaultValue="0" className="w-full">
            <TabsList className="grid w-full grid-cols-3 bg-slate-700/50 mb-8">
              {examples.map((example, index) => (
                <TabsTrigger
                  key={index}
                  value={index.toString()}
                  className="data-[state=active]:bg-purple-600 data-[state=active]:text-white"
                >
                  {example.category}
                </TabsTrigger>
              ))}
            </TabsList>

            {examples.map((example, index) => (
              <TabsContent key={index} value={index.toString()}>
                <Card className="bg-slate-800/50 border-slate-600">
                  <CardHeader>
                    <CardTitle className="text-white text-2xl">{example.title}</CardTitle>
                    <CardDescription className="text-gray-300 text-lg">
                      {example.description}
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="bg-black p-6 rounded-lg overflow-auto">
                      <pre className="text-green-400 text-xs font-mono leading-tight whitespace-pre">
                        {example.ascii}
                      </pre>
                    </div>
                  </CardContent>
                </Card>
              </TabsContent>
            ))}
          </Tabs>
        </div>
      </section>

      {/* Technical Deep Dive */}
      <section className="py-20 bg-slate-800/30">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-4xl font-bold text-white mb-4">How It Works</h2>
            <p className="text-xl text-gray-300 max-w-3xl mx-auto">
              Discover the advanced algorithms and techniques behind our ASCII art conversion process
            </p>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
            <div className="space-y-8">
              <div className="bg-slate-700/50 p-6 rounded-lg border border-slate-600">
                <h3 className="text-2xl font-bold text-white mb-4 flex items-center">
                  <Cpu className="h-6 w-6 mr-3 text-purple-400" />
                  Image Processing Pipeline
                </h3>
                <p className="text-gray-300 leading-relaxed mb-4">
                  Our sophisticated image processing pipeline begins with advanced color space analysis.
                  When you upload an image, our system immediately analyzes the color distribution,
                  brightness levels, and contrast ratios to determine the optimal conversion parameters.
                </p>
                <p className="text-gray-300 leading-relaxed">
                  The algorithm employs perceptual luminance calculations using the ITU-R BT.709 standard,
                  ensuring that the resulting ASCII art maintains the visual hierarchy and depth of the original image.
                </p>
              </div>

              <div className="bg-slate-700/50 p-6 rounded-lg border border-slate-600">
                <h3 className="text-2xl font-bold text-white mb-4 flex items-center">
                  <Code className="h-6 w-6 mr-3 text-purple-400" />
                  Character Mapping Algorithm
                </h3>
                <p className="text-gray-300 leading-relaxed mb-4">
                  Our proprietary character mapping system uses density-based analysis to match pixel
                  brightness values with ASCII characters. Each character in our sets has been carefully
                  analyzed for its visual density and stroke weight.
                </p>
                <p className="text-gray-300 leading-relaxed">
                  The system supports multiple character sets ranging from minimal (4 characters) to
                  detailed (14 characters), allowing for different artistic styles and output requirements.
                </p>
              </div>
            </div>

            <div className="space-y-6">
              <Card className="bg-slate-700/50 border-slate-600">
                <CardHeader>
                  <CardTitle className="text-white text-xl">Performance Metrics</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="flex justify-between items-center">
                    <span className="text-gray-300">Processing Speed</span>
                    <Badge variant="secondary" className="bg-green-100 text-green-800">
                      &lt; 100ms
                    </Badge>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-gray-300">Max Resolution</span>
                    <Badge variant="secondary" className="bg-blue-100 text-blue-800">
                      8K Support
                    </Badge>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-gray-300">Color Accuracy</span>
                    <Badge variant="secondary" className="bg-purple-100 text-purple-800">
                      99.7%
                    </Badge>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-gray-300">File Formats</span>
                    <Badge variant="secondary" className="bg-orange-100 text-orange-800">
                      15+ Types
                    </Badge>
                  </div>
                </CardContent>
              </Card>

              <Card className="bg-slate-700/50 border-slate-600">
                <CardHeader>
                  <CardTitle className="text-white text-xl">Supported Formats</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-3 gap-2 text-sm">
                    <Badge variant="outline" className="border-gray-500 text-gray-300">JPEG</Badge>
                    <Badge variant="outline" className="border-gray-500 text-gray-300">PNG</Badge>
                    <Badge variant="outline" className="border-gray-500 text-gray-300">GIF</Badge>
                    <Badge variant="outline" className="border-gray-500 text-gray-300">WebP</Badge>
                    <Badge variant="outline" className="border-gray-500 text-gray-300">BMP</Badge>
                    <Badge variant="outline" className="border-gray-500 text-gray-300">TIFF</Badge>
                    <Badge variant="outline" className="border-gray-500 text-gray-300">SVG</Badge>
                    <Badge variant="outline" className="border-gray-500 text-gray-300">AVIF</Badge>
                    <Badge variant="outline" className="border-gray-500 text-gray-300">HEIC</Badge>
                  </div>
                </CardContent>
              </Card>
            </div>
          </div>
        </div>
      </section>

      {/* Use Cases Section */}
      <section className="py-20">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-4xl font-bold text-white mb-4">Endless Possibilities</h2>
            <p className="text-xl text-gray-300 max-w-3xl mx-auto">
              From creative projects to technical documentation, ASCII art has applications across numerous fields
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            <Card className="bg-gradient-to-br from-purple-900/50 to-pink-900/50 border-purple-500/30 hover:scale-105 transition-transform duration-300">
              <CardHeader>
                <div className="text-purple-400 mb-4">
                  <Code className="h-8 w-8" />
                </div>
                <CardTitle className="text-white text-xl">Software Development</CardTitle>
              </CardHeader>
              <CardContent>
                <CardDescription className="text-gray-300 leading-relaxed">
                  Add personality to your code with ASCII art headers, comments, and documentation.
                  Perfect for README files, splash screens, and terminal applications that need visual flair.
                </CardDescription>
              </CardContent>
            </Card>

            <Card className="bg-gradient-to-br from-blue-900/50 to-cyan-900/50 border-blue-500/30 hover:scale-105 transition-transform duration-300">
              <CardHeader>
                <div className="text-blue-400 mb-4">
                  <ImageIcon className="h-8 w-8" />
                </div>
                <CardTitle className="text-white text-xl">Digital Art & Design</CardTitle>
              </CardHeader>
              <CardContent>
                <CardDescription className="text-gray-300 leading-relaxed">
                  Create unique digital artwork, social media posts, and design elements.
                  ASCII art brings a retro aesthetic that stands out in today's visual landscape.
                </CardDescription>
              </CardContent>
            </Card>

            <Card className="bg-gradient-to-br from-green-900/50 to-emerald-900/50 border-green-500/30 hover:scale-105 transition-transform duration-300">
              <CardHeader>
                <div className="text-green-400 mb-4">
                  <Users className="h-8 w-8" />
                </div>
                <CardTitle className="text-white text-xl">Education & Training</CardTitle>
              </CardHeader>
              <CardContent>
                <CardDescription className="text-gray-300 leading-relaxed">
                  Teach concepts of image processing, computer graphics, and digital art.
                  ASCII conversion provides an excellent introduction to pixel manipulation and algorithms.
                </CardDescription>
              </CardContent>
            </Card>

            <Card className="bg-gradient-to-br from-orange-900/50 to-red-900/50 border-orange-500/30 hover:scale-105 transition-transform duration-300">
              <CardHeader>
                <div className="text-orange-400 mb-4">
                  <Globe className="h-8 w-8" />
                </div>
                <CardTitle className="text-white text-xl">Web Development</CardTitle>
              </CardHeader>
              <CardContent>
                <CardDescription className="text-gray-300 leading-relaxed">
                  Enhance websites with unique ASCII elements, create loading screens,
                  or add retro styling to web applications. Perfect for developer portfolios and tech blogs.
                </CardDescription>
              </CardContent>
            </Card>

            <Card className="bg-gradient-to-br from-pink-900/50 to-rose-900/50 border-pink-500/30 hover:scale-105 transition-transform duration-300">
              <CardHeader>
                <div className="text-pink-400 mb-4">
                  <Star className="h-8 w-8" />
                </div>
                <CardTitle className="text-white text-xl">Social Media</CardTitle>
              </CardHeader>
              <CardContent>
                <CardDescription className="text-gray-300 leading-relaxed">
                  Stand out on platforms with unique ASCII profile pictures, posts, and stories.
                  Create viral content that captures attention in crowded social feeds.
                </CardDescription>
              </CardContent>
            </Card>

            <Card className="bg-gradient-to-br from-indigo-900/50 to-purple-900/50 border-indigo-500/30 hover:scale-105 transition-transform duration-300">
              <CardHeader>
                <div className="text-indigo-400 mb-4">
                  <Monitor className="h-8 w-8" />
                </div>
                <CardTitle className="text-white text-xl">Terminal Applications</CardTitle>
              </CardHeader>
              <CardContent>
                <CardDescription className="text-gray-300 leading-relaxed">
                  Build impressive command-line tools and terminal UIs with ASCII graphics.
                  Perfect for system administrators, DevOps tools, and CLI applications.
                </CardDescription>
              </CardContent>
            </Card>
          </div>
        </div>
      </section>

      {/* Responsive Design Section */}
      <section className="py-20 bg-slate-800/30">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-4xl font-bold text-white mb-4">Works Everywhere</h2>
            <p className="text-xl text-gray-300 max-w-3xl mx-auto">
              Our ASCII converter is fully responsive and optimized for all devices and screen sizes
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8 mb-12">
            <div className="text-center">
              <div className="bg-slate-700/50 p-8 rounded-lg border border-slate-600 mb-4">
                <Monitor className="h-16 w-16 text-purple-400 mx-auto mb-4" />
                <h3 className="text-xl font-bold text-white mb-2">Desktop</h3>
                <p className="text-gray-300">
                  Full-featured experience with resizable panels, advanced controls, and maximum screen real estate
                </p>
              </div>
            </div>

            <div className="text-center">
              <div className="bg-slate-700/50 p-8 rounded-lg border border-slate-600 mb-4">
                <Tablet className="h-16 w-16 text-purple-400 mx-auto mb-4" />
                <h3 className="text-xl font-bold text-white mb-2">Tablet</h3>
                <p className="text-gray-300">
                  Touch-optimized interface with intuitive gestures and adaptive layout for portrait and landscape modes
                </p>
              </div>
            </div>

            <div className="text-center">
              <div className="bg-slate-700/50 p-8 rounded-lg border border-slate-600 mb-4">
                <Smartphone className="h-16 w-16 text-purple-400 mx-auto mb-4" />
                <h3 className="text-xl font-bold text-white mb-2">Mobile</h3>
                <p className="text-gray-300">
                  Streamlined mobile experience with stacked layout and thumb-friendly controls for on-the-go creation
                </p>
              </div>
            </div>
          </div>

          <div className="bg-slate-700/30 p-8 rounded-lg border border-slate-600">
            <h3 className="text-2xl font-bold text-white mb-6 text-center">Cross-Platform Compatibility</h3>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-6 text-center">
              <div>
                <div className="text-3xl mb-2">🖥️</div>
                <p className="text-gray-300 font-medium">Windows</p>
              </div>
              <div>
                <div className="text-3xl mb-2">🍎</div>
                <p className="text-gray-300 font-medium">macOS</p>
              </div>
              <div>
                <div className="text-3xl mb-2">🐧</div>
                <p className="text-gray-300 font-medium">Linux</p>
              </div>
              <div>
                <div className="text-3xl mb-2">📱</div>
                <p className="text-gray-300 font-medium">Mobile</p>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Advanced Features Deep Dive */}
      <section className="py-20">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-4xl font-bold text-white mb-4">Advanced Features</h2>
            <p className="text-xl text-gray-300 max-w-3xl mx-auto">
              Dive deep into the sophisticated features that make our ASCII converter the most powerful tool available
            </p>
          </div>

          <div className="space-y-12">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
              <div>
                <h3 className="text-3xl font-bold text-white mb-6">Real-Time Preview</h3>
                <p className="text-gray-300 text-lg leading-relaxed mb-6">
                  Experience instant feedback with our real-time preview system. As you adjust settings like resolution,
                  character sets, or color modes, the ASCII output updates immediately, allowing you to fine-tune your
                  artwork with precision.
                </p>
                <p className="text-gray-300 text-lg leading-relaxed mb-6">
                  Our optimized rendering engine processes changes in under 100 milliseconds, providing a smooth and
                  responsive user experience that feels natural and intuitive.
                </p>
                <ul className="space-y-3 text-gray-300">
                  <li className="flex items-center">
                    <div className="w-2 h-2 bg-purple-400 rounded-full mr-3"></div>
                    Instant parameter updates
                  </li>
                  <li className="flex items-center">
                    <div className="w-2 h-2 bg-purple-400 rounded-full mr-3"></div>
                    Smooth resolution scaling
                  </li>
                  <li className="flex items-center">
                    <div className="w-2 h-2 bg-purple-400 rounded-full mr-3"></div>
                    Live color mode switching
                  </li>
                </ul>
              </div>
              <div className="bg-slate-800/50 p-6 rounded-lg border border-slate-600">
                <div className="bg-black p-4 rounded mb-4">
                  <pre className="text-green-400 text-xs font-mono">
{`    @@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@
    @@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@
    @@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@
    @@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@`}
                  </pre>
                </div>
                <p className="text-gray-400 text-sm text-center">Live preview updates as you adjust settings</p>
              </div>
            </div>

            <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
              <div className="order-2 lg:order-1">
                <Card className="bg-slate-800/50 border-slate-600">
                  <CardHeader>
                    <CardTitle className="text-white">Character Set Options</CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div className="bg-black p-3 rounded">
                      <p className="text-gray-400 text-sm mb-1">Standard:</p>
                      <code className="text-green-400 font-mono"> .:-=+*#%@</code>
                    </div>
                    <div className="bg-black p-3 rounded">
                      <p className="text-gray-400 text-sm mb-1">Detailed:</p>
                      <code className="text-green-400 font-mono"> .,:;i1tfLCG08@</code>
                    </div>
                    <div className="bg-black p-3 rounded">
                      <p className="text-gray-400 text-sm mb-1">Blocks:</p>
                      <code className="text-green-400 font-mono"> ░▒▓█</code>
                    </div>
                    <div className="bg-black p-3 rounded">
                      <p className="text-gray-400 text-sm mb-1">Minimal:</p>
                      <code className="text-green-400 font-mono"> .:█</code>
                    </div>
                  </CardContent>
                </Card>
              </div>
              <div className="order-1 lg:order-2">
                <h3 className="text-3xl font-bold text-white mb-6">Customizable Character Sets</h3>
                <p className="text-gray-300 text-lg leading-relaxed mb-6">
                  Choose from carefully curated character sets, each optimized for different artistic styles and output requirements.
                  Our character sets are scientifically arranged by visual density to ensure optimal contrast and readability.
                </p>
                <p className="text-gray-300 text-lg leading-relaxed mb-6">
                  From minimal 4-character sets perfect for simple graphics to detailed 14-character sets ideal for
                  photorealistic conversions, we provide the tools for every creative vision.
                </p>
                <ul className="space-y-3 text-gray-300">
                  <li className="flex items-center">
                    <div className="w-2 h-2 bg-purple-400 rounded-full mr-3"></div>
                    Density-optimized arrangements
                  </li>
                  <li className="flex items-center">
                    <div className="w-2 h-2 bg-purple-400 rounded-full mr-3"></div>
                    Multiple artistic styles
                  </li>
                  <li className="flex items-center">
                    <div className="w-2 h-2 bg-purple-400 rounded-full mr-3"></div>
                    Unicode block character support
                  </li>
                </ul>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Performance and Technical Excellence */}
      <section className="py-20 bg-slate-800/30">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-4xl font-bold text-white mb-4">Built for Performance</h2>
            <p className="text-xl text-gray-300 max-w-3xl mx-auto">
              Our converter is engineered with cutting-edge web technologies for maximum speed and reliability
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mb-12">
            <Card className="bg-slate-700/50 border-slate-600">
              <CardHeader>
                <CardTitle className="text-white flex items-center">
                  <Zap className="h-5 w-5 mr-2 text-yellow-400" />
                  Lightning Fast
                </CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-gray-300 mb-4">
                  Optimized algorithms process images in under 100ms, providing instant feedback and smooth user experience.
                </p>
                <div className="space-y-2">
                  <div className="flex justify-between">
                    <span className="text-sm text-gray-400">Small images (&lt;1MB)</span>
                    <span className="text-sm text-green-400">&lt;50ms</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm text-gray-400">Medium images (1-5MB)</span>
                    <span className="text-sm text-green-400">&lt;100ms</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm text-gray-400">Large images (&gt;5MB)</span>
                    <span className="text-sm text-green-400">&lt;200ms</span>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card className="bg-slate-700/50 border-slate-600">
              <CardHeader>
                <CardTitle className="text-white flex items-center">
                  <Settings className="h-5 w-5 mr-2 text-blue-400" />
                  Memory Efficient
                </CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-gray-300 mb-4">
                  Smart memory management ensures smooth operation even with large images and extended usage sessions.
                </p>
                <div className="space-y-2">
                  <div className="flex justify-between">
                    <span className="text-sm text-gray-400">Base memory usage</span>
                    <span className="text-sm text-blue-400">&lt;10MB</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm text-gray-400">Peak usage (8K image)</span>
                    <span className="text-sm text-blue-400">&lt;50MB</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm text-gray-400">Garbage collection</span>
                    <span className="text-sm text-blue-400">Automatic</span>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card className="bg-slate-700/50 border-slate-600">
              <CardHeader>
                <CardTitle className="text-white flex items-center">
                  <Globe className="h-5 w-5 mr-2 text-green-400" />
                  Browser Compatible
                </CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-gray-300 mb-4">
                  Works seamlessly across all modern browsers with progressive enhancement and graceful degradation.
                </p>
                <div className="space-y-2">
                  <div className="flex justify-between">
                    <span className="text-sm text-gray-400">Chrome/Edge</span>
                    <span className="text-sm text-green-400">Full support</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm text-gray-400">Firefox</span>
                    <span className="text-sm text-green-400">Full support</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm text-gray-400">Safari</span>
                    <span className="text-sm text-green-400">Full support</span>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </section>

      {/* Testimonials Section */}
      <section className="py-20">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-4xl font-bold text-white mb-4">What Users Say</h2>
            <p className="text-xl text-gray-300 max-w-3xl mx-auto">
              Join thousands of satisfied users who have transformed their creative projects with our ASCII converter
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            <Card className="bg-slate-700/50 border-slate-600 hover:bg-slate-700/70 transition-all duration-300">
              <CardHeader>
                <div className="flex items-center space-x-4">
                  <div className="w-12 h-12 bg-gradient-to-r from-purple-400 to-pink-400 rounded-full flex items-center justify-center text-white font-bold">
                    A
                  </div>
                  <div>
                    <CardTitle className="text-white text-lg">Alex Chen</CardTitle>
                    <CardDescription className="text-gray-400">Software Developer</CardDescription>
                  </div>
                </div>
              </CardHeader>
              <CardContent>
                <div className="flex mb-4">
                  {[...Array(5)].map((_, i) => (
                    <Star key={i} className="h-4 w-4 text-yellow-400 fill-current" />
                  ))}
                </div>
                <p className="text-gray-300 leading-relaxed">
                  "This ASCII converter has revolutionized how I create documentation headers and terminal splash screens.
                  The real-time preview and multiple character sets make it incredibly versatile for any project."
                </p>
              </CardContent>
            </Card>

            <Card className="bg-slate-700/50 border-slate-600 hover:bg-slate-700/70 transition-all duration-300">
              <CardHeader>
                <div className="flex items-center space-x-4">
                  <div className="w-12 h-12 bg-gradient-to-r from-blue-400 to-cyan-400 rounded-full flex items-center justify-center text-white font-bold">
                    S
                  </div>
                  <div>
                    <CardTitle className="text-white text-lg">Sarah Johnson</CardTitle>
                    <CardDescription className="text-gray-400">Digital Artist</CardDescription>
                  </div>
                </div>
              </CardHeader>
              <CardContent>
                <div className="flex mb-4">
                  {[...Array(5)].map((_, i) => (
                    <Star key={i} className="h-4 w-4 text-yellow-400 fill-current" />
                  ))}
                </div>
                <p className="text-gray-300 leading-relaxed">
                  "As a digital artist, I love experimenting with different mediums. This tool opened up a whole new
                  world of ASCII art possibilities. The color support is fantastic for creating unique social media content."
                </p>
              </CardContent>
            </Card>

            <Card className="bg-slate-700/50 border-slate-600 hover:bg-slate-700/70 transition-all duration-300">
              <CardHeader>
                <div className="flex items-center space-x-4">
                  <div className="w-12 h-12 bg-gradient-to-r from-green-400 to-emerald-400 rounded-full flex items-center justify-center text-white font-bold">
                    M
                  </div>
                  <div>
                    <CardTitle className="text-white text-lg">Mike Rodriguez</CardTitle>
                    <CardDescription className="text-gray-400">System Administrator</CardDescription>
                  </div>
                </div>
              </CardHeader>
              <CardContent>
                <div className="flex mb-4">
                  {[...Array(5)].map((_, i) => (
                    <Star key={i} className="h-4 w-4 text-yellow-400 fill-current" />
                  ))}
                </div>
                <p className="text-gray-300 leading-relaxed">
                  "Perfect for creating custom MOTD banners and terminal graphics. The performance is outstanding -
                  even large images convert instantly. It's become an essential tool in my workflow."
                </p>
              </CardContent>
            </Card>

            <Card className="bg-slate-700/50 border-slate-600 hover:bg-slate-700/70 transition-all duration-300">
              <CardHeader>
                <div className="flex items-center space-x-4">
                  <div className="w-12 h-12 bg-gradient-to-r from-orange-400 to-red-400 rounded-full flex items-center justify-center text-white font-bold">
                    E
                  </div>
                  <div>
                    <CardTitle className="text-white text-lg">Emily Watson</CardTitle>
                    <CardDescription className="text-gray-400">Content Creator</CardDescription>
                  </div>
                </div>
              </CardHeader>
              <CardContent>
                <div className="flex mb-4">
                  {[...Array(5)].map((_, i) => (
                    <Star key={i} className="h-4 w-4 text-yellow-400 fill-current" />
                  ))}
                </div>
                <p className="text-gray-300 leading-relaxed">
                  "My followers love the unique ASCII art posts I create with this tool. It's so easy to use and the
                  results are always impressive. The mobile version works great for creating content on the go."
                </p>
              </CardContent>
            </Card>

            <Card className="bg-slate-700/50 border-slate-600 hover:bg-slate-700/70 transition-all duration-300">
              <CardHeader>
                <div className="flex items-center space-x-4">
                  <div className="w-12 h-12 bg-gradient-to-r from-pink-400 to-rose-400 rounded-full flex items-center justify-center text-white font-bold">
                    D
                  </div>
                  <div>
                    <CardTitle className="text-white text-lg">David Kim</CardTitle>
                    <CardDescription className="text-gray-400">Computer Science Professor</CardDescription>
                  </div>
                </div>
              </CardHeader>
              <CardContent>
                <div className="flex mb-4">
                  {[...Array(5)].map((_, i) => (
                    <Star key={i} className="h-4 w-4 text-yellow-400 fill-current" />
                  ))}
                </div>
                <p className="text-gray-300 leading-relaxed">
                  "I use this in my computer graphics courses to demonstrate image processing concepts. Students love
                  seeing the immediate visual feedback, and it's a great introduction to pixel manipulation algorithms."
                </p>
              </CardContent>
            </Card>

            <Card className="bg-slate-700/50 border-slate-600 hover:bg-slate-700/70 transition-all duration-300">
              <CardHeader>
                <div className="flex items-center space-x-4">
                  <div className="w-12 h-12 bg-gradient-to-r from-indigo-400 to-purple-400 rounded-full flex items-center justify-center text-white font-bold">
                    L
                  </div>
                  <div>
                    <CardTitle className="text-white text-lg">Lisa Thompson</CardTitle>
                    <CardDescription className="text-gray-400">Web Designer</CardDescription>
                  </div>
                </div>
              </CardHeader>
              <CardContent>
                <div className="flex mb-4">
                  {[...Array(5)].map((_, i) => (
                    <Star key={i} className="h-4 w-4 text-yellow-400 fill-current" />
                  ))}
                </div>
                <p className="text-gray-300 leading-relaxed">
                  "This tool has added a unique retro element to several client projects. The ability to export in
                  different formats and the consistent quality across devices makes it invaluable for professional work."
                </p>
              </CardContent>
            </Card>
          </div>
        </div>
      </section>

      {/* FAQ Section */}
      <section className="py-20 bg-slate-800/30">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-4xl font-bold text-white mb-4">Frequently Asked Questions</h2>
            <p className="text-xl text-gray-300">
              Everything you need to know about our ASCII art converter
            </p>
          </div>

          <div className="space-y-6">
            <Card className="bg-slate-700/50 border-slate-600">
              <CardHeader>
                <CardTitle className="text-white text-xl">What image formats are supported?</CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-gray-300 leading-relaxed">
                  Our converter supports all major image formats including JPEG, PNG, GIF, WebP, BMP, TIFF, SVG, AVIF, and HEIC.
                  The system automatically detects the format and optimizes the conversion process accordingly. For best results,
                  we recommend using high-contrast images with clear subject matter.
                </p>
              </CardContent>
            </Card>

            <Card className="bg-slate-700/50 border-slate-600">
              <CardHeader>
                <CardTitle className="text-white text-xl">How does the resolution setting work?</CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-gray-300 leading-relaxed">
                  The resolution setting controls the density of the ASCII output. Lower values (0.05-0.1) create smaller,
                  more abstract representations, while higher values (0.2-0.3) produce detailed, larger ASCII art. The optimal
                  setting depends on your image content and intended use. Portrait photos typically work best at 0.15-0.25,
                  while landscapes and abstract images can use the full range.
                </p>
              </CardContent>
            </Card>

            <Card className="bg-slate-700/50 border-slate-600">
              <CardHeader>
                <CardTitle className="text-white text-xl">Can I use the ASCII art commercially?</CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-gray-300 leading-relaxed">
                  Yes! The ASCII art generated by our tool is yours to use however you like, including commercial projects.
                  However, please ensure you have the rights to use the original image you're converting. The ASCII conversion
                  process creates a derivative work, so the copyright of the original image still applies.
                </p>
              </CardContent>
            </Card>

            <Card className="bg-slate-700/50 border-slate-600">
              <CardHeader>
                <CardTitle className="text-white text-xl">What's the difference between grayscale and color modes?</CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-gray-300 leading-relaxed">
                  Grayscale mode converts images to traditional black and white ASCII art using brightness values only.
                  Color mode preserves the original colors of your image, creating colorful ASCII art where each character
                  retains the color from the corresponding pixel. Color mode is perfect for vibrant, artistic outputs,
                  while grayscale is ideal for traditional ASCII art and terminal applications.
                </p>
              </CardContent>
            </Card>

            <Card className="bg-slate-700/50 border-slate-600">
              <CardHeader>
                <CardTitle className="text-white text-xl">Is my uploaded image data secure?</CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-gray-300 leading-relaxed">
                  Absolutely. All image processing happens entirely in your browser using client-side JavaScript.
                  Your images are never uploaded to our servers or transmitted over the internet. This ensures complete
                  privacy and security for your images, while also providing the fastest possible processing speeds.
                </p>
              </CardContent>
            </Card>

            <Card className="bg-slate-700/50 border-slate-600">
              <CardHeader>
                <CardTitle className="text-white text-xl">Can I customize the character sets?</CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-gray-300 leading-relaxed">
                  Currently, we offer four carefully curated character sets: Standard (10 characters), Detailed (14 characters),
                  Blocks (4 Unicode block characters), and Minimal (3 characters). Each set is optimized for different artistic
                  styles and output requirements. We're working on custom character set support for future releases.
                </p>
              </CardContent>
            </Card>
          </div>
        </div>
      </section>

      {/* Final Call to Action */}
      <section className="py-20 bg-gradient-to-r from-purple-900/50 to-pink-900/50">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h2 className="text-4xl md:text-5xl font-bold text-white mb-6">
            Ready to Create Amazing ASCII Art?
          </h2>
          <p className="text-xl text-gray-300 mb-8 leading-relaxed">
            Join thousands of creators, developers, and artists who have discovered the power of ASCII art.
            Transform your images into stunning text-based artwork in seconds.
          </p>

          <div className="flex flex-col sm:flex-row gap-4 justify-center items-center mb-12">
            <Link href="/converter">
              <Button size="lg" className="bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700 text-white px-12 py-4 text-xl font-semibold rounded-full shadow-lg hover:shadow-xl transition-all duration-300">
                Start Converting Now
                <ArrowRight className="ml-3 h-6 w-6" />
              </Button>
            </Link>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8 text-center">
            <div>
              <div className="text-3xl font-bold text-white mb-2">100,000+</div>
              <div className="text-gray-300">Images Converted</div>
            </div>
            <div>
              <div className="text-3xl font-bold text-white mb-2">50,000+</div>
              <div className="text-gray-300">Happy Users</div>
            </div>
            <div>
              <div className="text-3xl font-bold text-white mb-2">99.9%</div>
              <div className="text-gray-300">Uptime</div>
            </div>
          </div>
        </div>
      </section>

      {/* Footer */}
      <footer className="bg-slate-900 border-t border-slate-700">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
            <div className="col-span-1 md:col-span-2">
              <h3 className="text-2xl font-bold text-white mb-4">ASCII Art Converter</h3>
              <p className="text-gray-300 leading-relaxed mb-6 max-w-md">
                The most advanced ASCII art converter on the web. Transform any image into beautiful
                text-based artwork with real-time preview and professional-grade output.
              </p>
              <div className="flex space-x-4">
                <Button variant="outline" size="sm" className="border-gray-600 text-gray-300 hover:bg-gray-700">
                  GitHub
                </Button>
                <Button variant="outline" size="sm" className="border-gray-600 text-gray-300 hover:bg-gray-700">
                  Twitter
                </Button>
                <Button variant="outline" size="sm" className="border-gray-600 text-gray-300 hover:bg-gray-700">
                  Discord
                </Button>
              </div>
            </div>

            <div>
              <h4 className="text-lg font-semibold text-white mb-4">Features</h4>
              <ul className="space-y-2 text-gray-300">
                <li><Link href="/converter" className="hover:text-white transition-colors">ASCII Converter</Link></li>
                <li><Link href="/examples" className="hover:text-white transition-colors">Examples Gallery</Link></li>
                <li><Link href="/api" className="hover:text-white transition-colors">API Documentation</Link></li>
                <li><Link href="/tutorials" className="hover:text-white transition-colors">Tutorials</Link></li>
              </ul>
            </div>

            <div>
              <h4 className="text-lg font-semibold text-white mb-4">Support</h4>
              <ul className="space-y-2 text-gray-300">
                <li><Link href="/help" className="hover:text-white transition-colors">Help Center</Link></li>
                <li><Link href="/contact" className="hover:text-white transition-colors">Contact Us</Link></li>
                <li><Link href="/privacy" className="hover:text-white transition-colors">Privacy Policy</Link></li>
                <li><Link href="/terms" className="hover:text-white transition-colors">Terms of Service</Link></li>
              </ul>
            </div>
          </div>

          <div className="border-t border-slate-700 mt-12 pt-8">
            <div className="flex flex-col md:flex-row justify-between items-center">
              <p className="text-gray-400 text-sm">
                © 2024 ASCII Art Converter. All rights reserved.
              </p>
              <p className="text-gray-400 text-sm mt-4 md:mt-0">
                Built with ❤️ using Next.js and React
              </p>
            </div>
          </div>
        </div>
      </footer>
    </div>
  )
}
